#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本要求第2问测试程序：K230直接控制步进电机和激光
测试五线四相步进电机的二维控制和激光发光功能
"""

import time
import os
import sys
from machine import FPIOA, Pin, PWM

# 五线四相步进电机控制类
class StepperMotor:
    def __init__(self, pin_a, pin_b, pin_c, pin_d, name="Motor"):
        """初始化步进电机控制
        Args:
            pin_a, pin_b, pin_c, pin_d: 四个控制引脚
            name: 电机名称（用于调试）
        """
        self.pins = [pin_a, pin_b, pin_c, pin_d]
        self.step_num = 0
        self.name = name
        # 五线四相步进电机的步进序列
        self.step_sequence = [
            [1, 1, 0, 0],  # 步骤1
            [0, 1, 1, 0],  # 步骤2
            [0, 0, 1, 1],  # 步骤3
            [1, 0, 0, 1]   # 步骤4
        ]
        print(f"{self.name} 初始化完成")
        
    def step_forward(self, steps=1):
        """正转指定步数"""
        print(f"{self.name} 正转 {steps} 步")
        for _ in range(steps):
            self.step_num = (self.step_num + 1) % 4
            self._set_pins()
            time.sleep_ms(5)  # 步进间隔，可以调整速度
            
    def step_backward(self, steps=1):
        """反转指定步数"""
        print(f"{self.name} 反转 {steps} 步")
        for _ in range(steps):
            self.step_num = (self.step_num - 1 + 4) % 4
            self._set_pins()
            time.sleep_ms(5)  # 步进间隔
            
    def _set_pins(self):
        """设置引脚状态"""
        step_status = self.step_sequence[self.step_num]
        for pin, value in zip(self.pins, step_status):
            pin.value(value)
            
    def stop(self):
        """停止电机（所有引脚设为低电平）"""
        for pin in self.pins:
            pin.value(0)
        print(f"{self.name} 停止")

# 激光控制类
class LaserController:
    def __init__(self, pwm_channel=3, gpio_pin=47):
        """初始化激光控制
        Args:
            pwm_channel: PWM通道号
            gpio_pin: GPIO引脚号
        """
        # 配置PWM引脚
        fpioa = FPIOA()
        fpioa.set_function(gpio_pin, getattr(FPIOA, f'PWM{pwm_channel}'))
        
        # 初始化PWM
        self.laser_pwm = PWM(pwm_channel, 2000, 0, enable=True)  # 2kHz, 初始0%占空比
        print(f"激光控制器初始化完成 - PWM{pwm_channel} GPIO{gpio_pin}")
        
    def turn_on(self, intensity=100):
        """开启激光
        Args:
            intensity: 激光强度 (0-100)
        """
        self.laser_pwm.duty(intensity)
        print(f"激光开启 - 强度: {intensity}%")
        
    def turn_off(self):
        """关闭激光"""
        self.laser_pwm.duty(0)
        print("激光关闭")
        
    def pulse(self, duration_ms=2000, intensity=100):
        """激光脉冲
        Args:
            duration_ms: 持续时间（毫秒）
            intensity: 激光强度 (0-100)
        """
        print(f"激光脉冲开始 - 持续{duration_ms}ms, 强度{intensity}%")
        self.turn_on(intensity)
        time.sleep_ms(duration_ms)
        self.turn_off()
        print("激光脉冲结束")

# 二维激光控制系统
class LaserTargetSystem:
    def __init__(self):
        """初始化二维激光控制系统"""
        print("=" * 50)
        print("基本要求第2问：K230直接控制步进电机和激光")
        print("初始化二维激光控制系统...")
        
        # 初始化FPIOA
        fpioa = FPIOA()
        
        # X轴步进电机配置 (控制左右移动)
        print("配置X轴步进电机 (左右控制)...")
        fpioa.set_function(15, FPIOA.GPIO15)
        fpioa.set_function(17, FPIOA.GPIO17)
        fpioa.set_function(16, FPIOA.GPIO16)
        fpioa.set_function(19, FPIOA.GPIO19)
        
        pin_x_a = Pin(15, Pin.OUT)
        pin_x_b = Pin(17, Pin.OUT)
        pin_x_c = Pin(16, Pin.OUT)
        pin_x_d = Pin(19, Pin.OUT)
        self.motor_x = StepperMotor(pin_x_a, pin_x_b, pin_x_c, pin_x_d, "X轴电机")
        
        # Y轴步进电机配置 (控制上下移动)
        print("配置Y轴步进电机 (上下控制)...")
        fpioa.set_function(35, FPIOA.GPIO35)
        fpioa.set_function(36, FPIOA.GPIO36)
        fpioa.set_function(34, FPIOA.GPIO34)
        fpioa.set_function(33, FPIOA.GPIO33)
        
        pin_y_a = Pin(35, Pin.OUT)
        pin_y_b = Pin(36, Pin.OUT)
        pin_y_c = Pin(34, Pin.OUT)
        pin_y_d = Pin(33, Pin.OUT)
        self.motor_y = StepperMotor(pin_y_a, pin_y_b, pin_y_c, pin_y_d, "Y轴电机")
        
        # 激光控制器配置
        print("配置激光控制器...")
        self.laser = LaserController(pwm_channel=3, gpio_pin=47)
        
        # 当前位置（以步数计算）
        self.current_x_steps = 0
        self.current_y_steps = 0
        
        print("二维激光控制系统初始化完成！")
        print("=" * 50)
        
    def move_relative(self, x_steps, y_steps):
        """相对移动
        Args:
            x_steps: X轴移动步数（正数向右，负数向左）
            y_steps: Y轴移动步数（正数向下，负数向上）
        """
        print(f"相对移动: X={x_steps}步, Y={y_steps}步")
        
        # X轴移动
        if x_steps > 0:
            self.motor_x.step_forward(x_steps)
        elif x_steps < 0:
            self.motor_x.step_backward(abs(x_steps))
            
        # Y轴移动
        if y_steps > 0:
            self.motor_y.step_forward(y_steps)
        elif y_steps < 0:
            self.motor_y.step_backward(abs(y_steps))
            
        # 更新当前位置
        self.current_x_steps += x_steps
        self.current_y_steps += y_steps
        print(f"当前位置: X={self.current_x_steps}步, Y={self.current_y_steps}步")
        
    def move_to_position(self, target_x_steps, target_y_steps):
        """移动到绝对位置
        Args:
            target_x_steps: 目标X位置（步数）
            target_y_steps: 目标Y位置（步数）
        """
        delta_x = target_x_steps - self.current_x_steps
        delta_y = target_y_steps - self.current_y_steps
        self.move_relative(delta_x, delta_y)
        
    def test_pattern(self):
        """测试模式：执行预定义的移动和射击模式"""
        print("\n开始测试模式...")
        
        # 测试1：基本移动
        print("\n测试1: 基本移动测试")
        self.move_relative(50, 0)    # 向右50步
        time.sleep_ms(500)
        self.move_relative(0, 50)    # 向下50步
        time.sleep_ms(500)
        self.move_relative(-50, 0)   # 向左50步
        time.sleep_ms(500)
        self.move_relative(0, -50)   # 向上50步
        time.sleep_ms(500)
        
        # 测试2：激光测试
        print("\n测试2: 激光测试")
        self.laser.pulse(1000, 50)   # 1秒脉冲，50%强度
        time.sleep_ms(500)
        self.laser.pulse(1000, 100)  # 1秒脉冲，100%强度
        
        # 测试3：组合测试（移动+射击）
        print("\n测试3: 移动+射击组合测试")
        positions = [(100, 100), (-100, 100), (-100, -100), (100, -100)]
        for i, (x, y) in enumerate(positions):
            print(f"移动到位置{i+1}: ({x}, {y})")
            self.move_to_position(x, y)
            time.sleep_ms(500)
            self.laser.pulse(500, 80)  # 0.5秒激光脉冲
            time.sleep_ms(500)
            
        # 回到原点
        print("\n回到原点")
        self.move_to_position(0, 0)
        
        print("\n测试模式完成！")
        
    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        self.motor_x.stop()
        self.motor_y.stop()
        self.laser.turn_off()
        print("资源清理完成")

def main():
    """主函数"""
    system = None
    try:
        # 初始化系统
        system = LaserTargetSystem()
        
        # 运行测试
        system.test_pattern()
        
        print("\n程序执行完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序异常: {e}")
    finally:
        if system:
            system.cleanup()

if __name__ == "__main__":
    main()
