import time
import os
import sys
import math

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import TOUCH
import time

sensor = None

# 五线四相步进电机控制类
class StepperMotor:
    def __init__(self, pin_a, pin_b, pin_c, pin_d):
        """初始化步进电机控制
        Args:
            pin_a, pin_b, pin_c, pin_d: 四个控制引脚
        """
        self.pins = [pin_a, pin_b, pin_c, pin_d]
        self.step_num = 0
        # 五线四相步进电机的步进序列
        self.step_sequence = [
            [1, 1, 0, 0],  # 步骤1
            [0, 1, 1, 0],  # 步骤2
            [0, 0, 1, 1],  # 步骤3
            [1, 0, 0, 1]   # 步骤4
        ]

    def step_forward(self, steps=1):
        """正转指定步数"""
        for _ in range(steps):
            self.step_num = (self.step_num + 1) % 4
            self._set_pins()
            time.sleep_ms(2)  # 步进间隔

    def step_backward(self, steps=1):
        """反转指定步数"""
        for _ in range(steps):
            self.step_num = (self.step_num - 1 + 4) % 4
            self._set_pins()
            time.sleep_ms(2)  # 步进间隔

    def _set_pins(self):
        """设置引脚状态"""
        step_status = self.step_sequence[self.step_num]
        for pin, value in zip(self.pins, step_status):
            pin.value(value)

    def stop(self):
        """停止电机（所有引脚设为低电平）"""
        for pin in self.pins:
            pin.value(0)

# A4纸张识别和打靶系统
class A4TargetSystem:
    def __init__(self):
        # A4纸张参数 (210mm x 297mm, 黑胶带宽度1.8cm)
        self.A4_RATIO = 297.0 / 210.0  # 约1.414
        self.A4_RATIO_TOLERANCE = 0.4  # 比例容差，考虑视角和胶带影响
        self.TAPE_WIDTH_MM = 18  # 黑胶带宽度18mm

        # 识别参数 - 针对黑胶带边框优化
        self.min_area = 8000      # 最小面积
        self.max_area = 300000    # 最大面积
        self.rect_threshold = 5000 # 矩形检测阈值

        # 二值化阈值 - 针对黑胶带边框
        self.black_threshold = [(0, 80)]    # 检测黑色胶带
        self.white_threshold = [(120, 255)] # 检测白色A4纸

        # 目标跟踪
        self.target_found = False
        self.target_center = (0, 0)
        self.target_stable_count = 0
        self.required_stable_frames = 5

        # 射击状态
        self.shoot_mode = False
        self.shoot_completed = False

        # 步进电机控制状态
        self.motor_x = None  # X轴步进电机
        self.motor_y = None  # Y轴步进电机
        self.laser_pin = None  # 激光控制引脚
        self.current_x = 400  # 当前X坐标（屏幕中心）
        self.current_y = 240  # 当前Y坐标（屏幕中心）
        self.steps_per_pixel_x = 2  # X轴每像素对应的步数
        self.steps_per_pixel_y = 2  # Y轴每像素对应的步数

        # 脱机调试系统
        self.debug_mode = False
        self.current_param = 0
        self.param_names = ["L_MIN", "L_MAX", "A_MIN", "A_MAX", "B_MIN", "B_MAX", "GRAY_MIN", "GRAY_MAX", "AREA_MIN", "AREA_MAX"]
        self.debug_params = {
            'L_MIN': 0, 'L_MAX': 100,      # LAB的L通道
            'A_MIN': -128, 'A_MAX': 127,   # LAB的A通道
            'B_MIN': -128, 'B_MAX': 127,   # LAB的B通道
            'GRAY_MIN': 0, 'GRAY_MAX': 80, # 灰度阈值
            'AREA_MIN': 8000, 'AREA_MAX': 300000
        }
        self.adjust_steps = {
            'L_MIN': 1, 'L_MAX': 1,
            'A_MIN': 2, 'A_MAX': 2,
            'B_MIN': 2, 'B_MAX': 2,
            'GRAY_MIN': 1, 'GRAY_MAX': 1,
            'AREA_MIN': 500, 'AREA_MAX': 5000
        }
        self.touch_start_time = 0
        self.long_press_threshold = 1000
        self.touch_count = 0
        self.button_hold_time = 0
        self.last_button = None

        # 稳定性滤波
        self.center_history = []
        self.max_history = 5  # 保存最近5帧的中心点

        # 加载保存的阈值
        self.load_thresholds()

    def init_hardware(self):
        """初始化硬件：两个步进电机（X轴和Y轴）和激光"""
        # 配置步进电机引脚
        fpioa = FPIOA()

        # X轴步进电机引脚配置 (使用GPIO 15, 17, 16, 19)
        # 这个电机控制左右移动
        fpioa.set_function(15, FPIOA.GPIO15)
        fpioa.set_function(17, FPIOA.GPIO17)
        fpioa.set_function(16, FPIOA.GPIO16)
        fpioa.set_function(19, FPIOA.GPIO19)

        pin_x_a = Pin(15, Pin.OUT)
        pin_x_b = Pin(17, Pin.OUT)
        pin_x_c = Pin(16, Pin.OUT)
        pin_x_d = Pin(19, Pin.OUT)
        self.motor_x = StepperMotor(pin_x_a, pin_x_b, pin_x_c, pin_x_d)

        # Y轴步进电机引脚配置 (使用GPIO 35, 36, 34, 33)
        # 这个电机控制上下移动
        fpioa.set_function(35, FPIOA.GPIO35)
        fpioa.set_function(36, FPIOA.GPIO36)
        fpioa.set_function(34, FPIOA.GPIO34)
        fpioa.set_function(33, FPIOA.GPIO33)

        pin_y_a = Pin(35, Pin.OUT)
        pin_y_b = Pin(36, Pin.OUT)
        pin_y_c = Pin(34, Pin.OUT)
        pin_y_d = Pin(33, Pin.OUT)
        self.motor_y = StepperMotor(pin_y_a, pin_y_b, pin_y_c, pin_y_d)

        # 激光控制引脚配置 (使用PWM3，引脚47)
        fpioa.set_function(47, FPIOA.PWM3)
        self.laser_pwm = PWM(3, 2000, 0, enable=True)  # 2kHz, 初始0%占空比

        print("硬件初始化完成：X轴步进电机、Y轴步进电机和激光已配置")

    def control_laser(self, enable=True):
        """控制激光开关"""
        if self.laser_pwm:
            if enable:
                self.laser_pwm.duty(100)  # 100%占空比，激光开启
                print("激光开启")
            else:
                self.laser_pwm.duty(0)    # 0%占空比，激光关闭
                print("激光关闭")

    def move_to_target(self, target_x, target_y):
        """移动激光到目标位置
        使用两个步进电机分别控制X轴（左右）和Y轴（上下）运动
        """
        if not self.motor_x or not self.motor_y:
            print("步进电机未初始化")
            return

        # 计算需要移动的距离（像素）
        delta_x = target_x - self.current_x
        delta_y = target_y - self.current_y

        # 转换为步数
        steps_x = int(abs(delta_x) * self.steps_per_pixel_x)
        steps_y = int(abs(delta_y) * self.steps_per_pixel_y)

        print(f"移动到目标: ({target_x}, {target_y})")
        print(f"当前位置: ({self.current_x}, {self.current_y})")
        print(f"移动距离: X={delta_x}px, Y={delta_y}px")
        print(f"步进步数: X={steps_x}步, Y={steps_y}步")

        # X轴移动（左右控制）
        if delta_x > 0:
            print(f"X轴正转{steps_x}步（向右）")
            self.motor_x.step_forward(steps_x)
        elif delta_x < 0:
            print(f"X轴反转{steps_x}步（向左）")
            self.motor_x.step_backward(steps_x)

        # Y轴移动（上下控制）
        if delta_y > 0:
            print(f"Y轴正转{steps_y}步（向下）")
            self.motor_y.step_forward(steps_y)
        elif delta_y < 0:
            print(f"Y轴反转{steps_y}步（向上）")
            self.motor_y.step_backward(steps_y)

        # 更新当前位置
        self.current_x = target_x
        self.current_y = target_y
        print(f"移动完成，当前位置: ({self.current_x}, {self.current_y})")

    def shoot_laser(self, duration_ms=100):
        """激光射击 - 快速射击一次留下痕迹点
        Args:
            duration_ms: 射击持续时间（毫秒），默认100ms，快速射击
        """
        print(f"激光射击 - 持续时间: {duration_ms}ms")
        self.control_laser(True)
        time.sleep_ms(duration_ms)
        self.control_laser(False)
        print("激光射击完成")

    def which_button(self, x, y):
        """判断按下的是哪个按钮"""
        # 返回按钮
        if x < 100 and y < 40:
            return "exit"

        # 参数选择按钮（左侧）
        if x < 120 and y >= 50:
            param_index = (y - 50) // 35
            if param_index < len(self.param_names) and (y - 50) % 35 < 30:
                return f"param_{param_index}"

        # 减少按钮
        if 680 <= x <= 780 and 120 <= y <= 180:
            return "minus"

        # 增加按钮
        if 680 <= x <= 780 and 200 <= y <= 260:
            return "plus"

        return None

    def handle_touch_debug(self, touch_x, touch_y, is_holding=False):
        """处理调试模式下的触摸"""
        if not self.debug_mode:
            return False

        button = self.which_button(touch_x, touch_y)
        if not button:
            self.last_button = None
            self.button_hold_time = 0
            return False

        # 长按加速逻辑
        if button == self.last_button and is_holding:
            self.button_hold_time += 1
        else:
            self.button_hold_time = 0
            self.last_button = button

        # 根据长按时间调整步长
        speed_multiplier = 1
        if self.button_hold_time > 10:
            speed_multiplier = 5
        elif self.button_hold_time > 20:
            speed_multiplier = 10

        if button == "exit":
            self.debug_mode = False
            self.save_thresholds()  # 退出时自动保存
            print("退出调试模式，阈值已保存")
            return True

        elif button.startswith("param_"):
            param_index = int(button.split("_")[1])
            self.current_param = param_index
            return True

        elif button == "minus":
            param_name = self.param_names[self.current_param]
            step = self.adjust_steps[param_name] * speed_multiplier
            min_val = self.get_param_min_value(param_name)
            self.debug_params[param_name] = max(min_val, self.debug_params[param_name] - step)
            self.update_thresholds()
            return True

        elif button == "plus":
            param_name = self.param_names[self.current_param]
            step = self.adjust_steps[param_name] * speed_multiplier
            max_val = self.get_param_max_value(param_name)
            self.debug_params[param_name] = min(max_val, self.debug_params[param_name] + step)
            self.update_thresholds()
            return True

        return False

    def get_param_min_value(self, param_name):
        min_values = {
            'L_MIN': 0, 'L_MAX': 0,
            'A_MIN': -128, 'A_MAX': -128,
            'B_MIN': -128, 'B_MAX': -128,
            'GRAY_MIN': 0, 'GRAY_MAX': 0,
            'AREA_MIN': 100, 'AREA_MAX': 100
        }
        return min_values.get(param_name, 0)

    def get_param_max_value(self, param_name):
        max_values = {
            'L_MIN': 100, 'L_MAX': 100,
            'A_MIN': 127, 'A_MAX': 127,
            'B_MIN': 127, 'B_MAX': 127,
            'GRAY_MIN': 255, 'GRAY_MAX': 255,
            'AREA_MIN': 500000, 'AREA_MAX': 1000000
        }
        return max_values.get(param_name, 1000)

    def save_thresholds(self):
        """保存阈值到文件"""
        try:
            import json
            threshold_data = {
                'debug_params': self.debug_params,
                'timestamp': time.ticks_ms()
            }
            with open('/sdcard/thresholds.json', 'w') as f:
                json.dump(threshold_data, f)
            print("阈值已保存到 /sdcard/thresholds.json")
        except Exception as e:
            print(f"保存阈值失败: {e}")

    def load_thresholds(self):
        """从文件加载阈值"""
        try:
            import json
            with open('/sdcard/thresholds.json', 'r') as f:
                threshold_data = json.load(f)

            if 'debug_params' in threshold_data:
                self.debug_params.update(threshold_data['debug_params'])
                self.update_thresholds()
                print("已加载保存的阈值")
        except Exception as e:
            print(f"加载阈值失败，使用默认值: {e}")

    def smooth_center(self, new_center):
        """平滑中心点，减少抖动"""
        if new_center is None:
            return None

        # 添加新的中心点到历史记录
        self.center_history.append(new_center)

        # 保持历史记录长度
        if len(self.center_history) > self.max_history:
            self.center_history.pop(0)

        # 如果历史记录不足，直接返回当前点
        if len(self.center_history) < 3:
            return new_center

        # 计算加权平均（最新的点权重更大）
        total_weight = 0
        weighted_x = 0
        weighted_y = 0

        for i, (x, y) in enumerate(self.center_history):
            weight = i + 1  # 权重递增
            weighted_x += x * weight
            weighted_y += y * weight
            total_weight += weight

        smooth_x = int(weighted_x / total_weight)
        smooth_y = int(weighted_y / total_weight)

        return (smooth_x, smooth_y)

    def update_thresholds(self):
        """根据调试参数更新阈值"""
        self.black_threshold = [(self.debug_params['BLACK_MIN'], self.debug_params['BLACK_MAX'])]
        self.white_threshold = [(self.debug_params['WHITE_MIN'], self.debug_params['WHITE_MAX'])]
        self.min_area = self.debug_params['AREA_MIN']
        self.max_area = self.debug_params['AREA_MAX']
        self.A4_RATIO_TOLERANCE = self.debug_params['RATIO_TOL'] * 0.01

    def draw_debug_interface(self, img, preview_img=None):
        if not self.debug_mode:
            return

        # 创建调试界面背景
        img.draw_rectangle(0, 0, 800, 480, color=(50, 50, 50), thickness=2, fill=True)

        # 返回按钮
        img.draw_rectangle(0, 0, 100, 40, color=(255, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(15, 10, 18, "SAVE&EXIT", color=(0, 0, 0))

        # 实时预览区域 - 中间
        if preview_img:
            try:
                # 简单的预览显示
                preview_x, preview_y = 250, 120
                img.draw_image(preview_img, preview_x, preview_y)
            except Exception as e:
                print(f"预览显示错误: {e}")
                img.draw_string_advanced(250, 200, 20, "Preview Error", color=(255, 0, 0))
        else:
            # 没有预览图像时显示提示
            img.draw_string_advanced(300, 200, 20, "No Preview", color=(128, 128, 128))

        # 预览区域边框
        img.draw_rectangle(250, 120, 300, 240, color=(255, 255, 255), thickness=2, fill=False)
        img.draw_string_advanced(250, 100, 20, "Real-time Preview", color=(255, 255, 255))

        # 参数选择按钮 - 左侧（更紧凑）
        for i, param_name in enumerate(self.param_names):
            y_pos = 50 + i * 35
            if y_pos > 450:  # 防止超出屏幕
                break
            color = (100, 255, 100) if i == self.current_param else (150, 150, 150)
            img.draw_rectangle(0, y_pos, 120, 30, color=color, thickness=1, fill=True)
            img.draw_string_advanced(5, y_pos + 5, 16, param_name, color=(0, 0, 0))

        # 调整按钮 - 右侧
        # 减少按钮
        img.draw_rectangle(680, 120, 100, 60, color=(255, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(720, 140, 30, "-", color=(0, 0, 0))

        # 增加按钮
        img.draw_rectangle(680, 200, 100, 60, color=(100, 255, 100), thickness=2, fill=True)
        img.draw_string_advanced(720, 220, 30, "+", color=(0, 0, 0))

        # 显示当前参数值
        current_param = self.param_names[self.current_param]
        current_value = self.debug_params[current_param]
        img.draw_rectangle(580, 300, 200, 60, color=(200, 200, 200), thickness=2, fill=True)
        img.draw_string_advanced(590, 315, 18, f"{current_param}", color=(0, 0, 0))
        img.draw_string_advanced(590, 335, 18, f"Value: {current_value}", color=(0, 0, 0))

        # 显示当前阈值
        img.draw_string_advanced(250, 370, 16, f"LAB: L({self.debug_params['L_MIN']}-{self.debug_params['L_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 390, 16, f"     A({self.debug_params['A_MIN']}-{self.debug_params['A_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 410, 16, f"     B({self.debug_params['B_MIN']}-{self.debug_params['B_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 430, 16, f"GRAY: ({self.debug_params['GRAY_MIN']}-{self.debug_params['GRAY_MAX']})", color=(255, 255, 255))
        img.draw_string_advanced(250, 450, 16, f"AREA: ({self.debug_params['AREA_MIN']}-{self.debug_params['AREA_MAX']})", color=(255, 255, 255))

        # 保存提示
        img.draw_string_advanced(10, 460, 14, "Auto-save on exit", color=(100, 255, 100))

    def handle_debug_keys(self):
        """处理脱机调试按键"""
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, self.last_key_time) < 200:  # 200ms防抖
            return False

        # 这里需要根据实际硬件添加按键检测
        # 假设有4个按键：上、下、左、右
        key_pressed = False

        if key_pressed:
            self.last_key_time = current_time

        return key_pressed

    def update_thresholds(self):
        # 更新LAB阈值
        self.lab_threshold = [(
            self.debug_params['L_MIN'], self.debug_params['L_MAX'],
            self.debug_params['A_MIN'], self.debug_params['A_MAX'],
            self.debug_params['B_MIN'], self.debug_params['B_MAX']
        )]
        # 更新灰度阈值
        self.black_threshold = [(self.debug_params['GRAY_MIN'], self.debug_params['GRAY_MAX'])]
        # 更新面积阈值
        self.min_area = self.debug_params['AREA_MIN']
        self.max_area = self.debug_params['AREA_MAX']

    def get_preview_image(self, img):
        """获取预览图像 - 显示当前阈值效果"""
        try:
            # 简化预览功能，只显示灰度二值化
            current_param = self.param_names[self.current_param]

            if current_param.startswith('GRAY_'):
                # 灰度阈值预览
                gray_img = img.to_grayscale()
                binary_img = gray_img.binary(self.black_threshold)
                return binary_img.to_rgb565()
            else:
                # 其他情况显示原图
                return img.copy()
        except Exception as e:
            print(f"预览图像错误: {e}")
            return None

    def is_a4_rectangle(self, rect):
        """判断是否为A4纸张矩形"""
        corners = rect.corners()

        # 计算矩形的宽度和高度
        width1 = math.sqrt((corners[1][0] - corners[0][0])**2 + (corners[1][1] - corners[0][1])**2)
        height1 = math.sqrt((corners[3][0] - corners[0][0])**2 + (corners[3][1] - corners[0][1])**2)
        width2 = math.sqrt((corners[2][0] - corners[3][0])**2 + (corners[2][1] - corners[3][1])**2)
        height2 = math.sqrt((corners[2][0] - corners[1][0])**2 + (corners[2][1] - corners[1][1])**2)

        # 取平均值
        avg_width = (width1 + width2) / 2
        avg_height = (height1 + height2) / 2

        # 确保长边作为高度
        if avg_width > avg_height:
            avg_width, avg_height = avg_height, avg_width

        # 检查长宽比
        if avg_height > 0:
            ratio = avg_height / avg_width
            if abs(ratio - self.A4_RATIO) < self.A4_RATIO_TOLERANCE:
                return True
        return False

# 初始化变量
touch = None

try:
    # 系统初始化
    target_system = A4TargetSystem()

    # 初始化硬件（步进电机和激光）
    target_system.init_hardware()

    # FPIOA配置
    fpioa = FPIOA()
    fpioa.set_function(53, FPIOA.GPIO53)

    # 硬件初始化
    key = Pin(53, Pin.IN, Pin.PULL_DOWN)

    # 摄像头初始化
    sensor = Sensor(width=800, height=480)
    sensor.reset()
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    # 显示初始化
    Display.init(Display.ST7701, width=800, height=480, to_ide=False)
    MediaManager.init()
    sensor.run()

    # 触摸屏初始化
    touch = TOUCH(0)

    clock = time.clock()
    print("系统初始化完成")
    print("=" * 60)
    print("基本要求第2问：K230自动激光打靶系统")
    print("硬件配置：")
    print("   - X轴步进电机：GPIO 15,17,16,19 (控制左右移动)")
    print("   - Y轴步进电机：GPIO 35,36,34,33 (控制上下移动)")
    print("   - 激光控制：PWM3 GPIO47 (蓝紫色激光)")
    print("工作流程：")
    print("   1. 自动识别A4纸张目标中心")
    print("   2. 步进电机瞄准目标中心")
    print("   3. 激光快速射击一次留下痕迹点")
    print("   4. 整个过程在2秒内完成")
    print("   5. 测量激光痕迹到中心点的最大误差")
    print("=" * 60)

    # 自动打靶模式 - 上电即开始工作
    frame_count = 0
    start_time = time.ticks_ms()
    target_locked = False
    shoot_executed = False
    auto_shoot_delay = 200  # 目标锁定后200ms开始射击，确保定位稳定
    target_lock_time = 0

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)
        frame_count += 1

        # 触摸检测 - 借鉴参考代码的思想
        touch_points = touch.read()
        if touch_points:
            target_system.touch_count += 1
            if target_system.touch_count > 10:  # 持续触摸10帧进入调试模式
                target_system.debug_mode = True
                target_system.touch_count = 0
        else:
            target_system.touch_count -= 2
            target_system.touch_count = max(0, target_system.touch_count)

        if target_system.debug_mode:
            # 在调试模式下处理触摸
            is_touching = len(touch_points) > 0
            if is_touching:
                for point in touch_points:
                    target_system.handle_touch_debug(point.x, point.y, is_holding=True)
            else:
                target_system.last_button = None
                target_system.button_hold_time = 0

            # 获取预览图像
            preview_img = target_system.get_preview_image(img)
            target_system.draw_debug_interface(img, preview_img)
            Display.show_image(img, x=0, y=0)
            time.sleep_ms(50)  # 调试模式下稍微降低帧率，提高稳定性
            continue

        elapsed_time = time.ticks_diff(time.ticks_ms(), start_time)
        remaining_time = max(0, 2000 - elapsed_time)

        # A4纸张识别
        target_found = False
        best_rect = None
        best_center = None

        # 转换为灰度图像进行矩形检测
        img_gray = img.to_grayscale(copy=True)

        # 使用二值化检测黑色边框
        img_binary = img_gray.binary(target_system.black_threshold)

        # 形态学处理减少抖动和噪点
        try:
            # 开运算：去除小噪点
            img_binary = img_binary.erode(1, threshold=1)
            img_binary = img_binary.dilate(1, threshold=1)
            # 闭运算：填补小空洞
            img_binary = img_binary.dilate(1, threshold=1)
            img_binary = img_binary.erode(1, threshold=1)
        except:
            # 如果形态学操作不支持，跳过
            pass

        # 查找矩形
        rects = img_binary.find_rects(threshold=target_system.rect_threshold)

        if rects:
            for rect in rects:
                # 检查面积
                area = rect.w() * rect.h()
                if area < target_system.min_area or area > target_system.max_area:
                    continue

                # 检查长宽比 (A4纸张比例)
                ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())
                if abs(ratio - target_system.A4_RATIO) > target_system.A4_RATIO_TOLERANCE:
                    continue

                # 找到有效的A4目标
                target_found = True
                best_rect = rect
                raw_center = (rect.x() + rect.w()//2, rect.y() + rect.h()//2)
                # 应用平滑滤波减少抖动
                best_center = target_system.smooth_center(raw_center)

                # 绘制检测到的矩形
                corners = rect.corners()
                img.draw_line(corners[0][0], corners[0][1], corners[1][0], corners[1][1], color=(0, 255, 0), thickness=3)
                img.draw_line(corners[1][0], corners[1][1], corners[2][0], corners[2][1], color=(0, 255, 0), thickness=3)
                img.draw_line(corners[2][0], corners[2][1], corners[3][0], corners[3][1], color=(0, 255, 0), thickness=3)
                img.draw_line(corners[3][0], corners[3][1], corners[0][0], corners[0][1], color=(0, 255, 0), thickness=3)

                # 绘制中心点
                img.draw_circle(best_center[0], best_center[1], 10, color=(255, 0, 0), thickness=3, fill=False)
                img.draw_cross(best_center[0], best_center[1], color=(255, 0, 0), size=20, thickness=2)

                break  # 只处理第一个有效目标

        # 🎯 自动打靶逻辑
        if target_found and not target_locked:
            target_system.target_center = best_center
            target_locked = True
            target_lock_time = time.ticks_ms()  # 记录目标锁定时间

            print(f"目标锁定成功: 中心点({best_center[0]}, {best_center[1]})")
            print("开始移动激光到目标中心...")

            # 移动激光到目标中心位置
            target_system.move_to_target(best_center[0], best_center[1])
            print("激光定位完成")

        # 目标锁定后，等待一段时间确保定位稳定，然后快速射击
        if target_locked and not shoot_executed:
            if 'target_lock_time' in locals():
                time_since_lock = time.ticks_diff(time.ticks_ms(), target_lock_time)
                if time_since_lock >= auto_shoot_delay:
                    print("瞄准完成，开始射击...")
                    target_system.shoot_laser(100)  # 快速射击100ms
                    shoot_executed = True
                    print("射击完成！")

        # 如果2秒内未发现目标，射击屏幕中心作为备选
        if remaining_time <= 0 and not shoot_executed:
            if not target_locked:
                print("未发现目标，射击屏幕中心")
                target_system.move_to_target(400, 240)
            else:
                print("射击已锁定的目标")
            target_system.shoot_laser(100)  # 快速射击
            shoot_executed = True

        img.draw_string_advanced(10, 10, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 0))
        img.draw_string_advanced(10, 40, 25, f"Time: {remaining_time}ms", color=(255, 0, 0))

        # 显示触摸提示
        if target_system.touch_count > 0:
            img.draw_string_advanced(10, 70, 20, f"Hold: {target_system.touch_count}/10", color=(255, 255, 0))
        else:
            img.draw_string_advanced(10, 70, 20, "Hold screen for debug", color=(128, 128, 128))

        if target_locked:
            img.draw_string_advanced(10, 100, 20, f"Target: {target_system.target_center}", color=(0, 255, 0))

        if shoot_executed:
            img.draw_string_advanced(10, 130, 25, "SHOT COMPLETED!", color=(255, 0, 0))
            img.draw_string_advanced(10, 160, 20, "Laser OFF - Check accuracy", color=(0, 255, 0))

        center_x, center_y = 400, 240
        img.draw_line(center_x-10, center_y, center_x+10, center_y, color=(128, 128, 128), thickness=1)
        img.draw_line(center_x, center_y-10, center_x, center_y+10, color=(128, 128, 128), thickness=1)

        if target_found:
            img.draw_rectangle(best_rect.x(), best_rect.y(), best_rect.w(), best_rect.h(),
                             color=(0, 255, 0), thickness=2)
            img.draw_circle(best_center[0], best_center[1], 3, color=(255, 0, 0), thickness=2)

        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    if touch is not None:
        touch.deinit()
    # 停止步进电机
    if hasattr(target_system, 'motor_x') and target_system.motor_x:
        target_system.motor_x.stop()
    if hasattr(target_system, 'motor_y') and target_system.motor_y:
        target_system.motor_y.stop()
    # 关闭激光
    if hasattr(target_system, 'laser_pwm') and target_system.laser_pwm:
        target_system.control_laser(False)
        target_system.laser_pwm.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
