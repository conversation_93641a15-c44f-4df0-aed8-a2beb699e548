# 基本要求第2问：K230直接控制步进电机和激光

## 概述

本程序实现了基本要求的第2问，使用K230开发板直接控制五线四相步进电机和激光发光，不再需要MSPM0G3507的串口通信。

## 硬件配置

### 步进电机连接

#### X轴步进电机（控制左右移动）
- GPIO 15 → 步进电机 A 相
- GPIO 17 → 步进电机 B 相  
- GPIO 16 → 步进电机 C 相
- GPIO 19 → 步进电机 D 相

#### Y轴步进电机（控制上下移动）
- GPIO 35 → 步进电机 A 相
- GPIO 36 → 步进电机 B 相
- GPIO 34 → 步进电机 C 相
- GPIO 33 → 步进电机 D 相

### 激光控制
- GPIO 47 → PWM3 输出，控制激光发光

## 工作原理

### 五线四相步进电机控制
```python
# 步进序列
step_sequence = [
    [1, 1, 0, 0],  # 步骤1
    [0, 1, 1, 0],  # 步骤2
    [0, 0, 1, 1],  # 步骤3
    [1, 0, 0, 1]   # 步骤4
]
```

### 二维运动控制
- **X轴电机**：控制激光左右移动
  - 正转：向右移动
  - 反转：向左移动
- **Y轴电机**：控制激光上下移动
  - 正转：向下移动
  - 反转：向上移动

### 激光控制
- 使用PWM控制激光强度
- 频率：2kHz
- 占空比：0-100%（控制激光强度）

## 程序功能

### 主要功能
1. **A4纸张识别**：识别带黑胶带边框的A4纸张
2. **目标跟踪**：实时跟踪目标位置
3. **二维运动控制**：使用两个步进电机控制激光位置
4. **激光射击**：在指定时间进行激光射击

### 工作流程
1. 系统启动，初始化摄像头、步进电机和激光
2. 实时识别A4纸张目标
3. 当发现目标时，控制步进电机移动激光到目标位置
4. 1.8秒后开始激光射击，持续2秒
5. 如果2秒内未发现目标，激光射击屏幕中心

## 文件说明

### main.py
主程序文件，包含完整的A4纸张识别和激光控制功能。

### test_stepper_laser.py
测试程序，专门用于验证步进电机和激光控制功能：
- 基本移动测试
- 激光强度测试
- 移动+射击组合测试

## 使用方法

### 运行主程序
```bash
python main.py
```

### 运行测试程序
```bash
python test_stepper_laser.py
```

### 调试模式
在主程序中，长按屏幕可进入调试模式，调整识别参数。

## 参数配置

### 步进电机参数
```python
self.steps_per_pixel_x = 2  # X轴每像素对应的步数
self.steps_per_pixel_y = 2  # Y轴每像素对应的步数
```

### 激光参数
```python
# PWM配置
pwm_channel = 3      # PWM通道
frequency = 2000     # 频率 2kHz
duty_cycle = 100     # 占空比 0-100%
```

### A4纸张识别参数
```python
self.A4_RATIO = 297.0 / 210.0  # A4纸张长宽比
self.A4_RATIO_TOLERANCE = 0.4   # 比例容差
self.min_area = 8000            # 最小面积
self.max_area = 300000          # 最大面积
```

## 注意事项

1. **硬件连接**：确保步进电机和激光正确连接到指定GPIO引脚
2. **电源供应**：步进电机需要足够的电源供应
3. **安全使用**：激光使用时注意安全，避免直视激光
4. **调试建议**：先运行测试程序验证硬件连接
5. **参数调整**：根据实际硬件调整步进电机的步数和激光强度

## 技术特点

1. **无需外部MCU**：直接使用K230控制，简化系统架构
2. **实时控制**：实时图像识别和运动控制
3. **精确定位**：使用步进电机实现精确的二维定位
4. **可调参数**：支持在线调试和参数调整
5. **模块化设计**：步进电机、激光控制和图像识别模块化

## 扩展功能

可以在此基础上扩展：
- 多目标跟踪
- 运动轨迹规划
- 激光功率自适应调节
- 更复杂的图像识别算法
